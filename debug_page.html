<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <link rel="dns-prefetch" href="https://h5.sinaimg.cn">
    <meta id="viewport" name="viewport"
          content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <title>微博-出错了</title>
    <style>
        html {
            font-size: 2rem;
        }

        @media (max-width: 1024px) {
            html {
                font-size: 1.25rem;
            }
        }

        @media (max-width: 414px) {
            html {
                font-size: 1.06rem;
            }
        }

        @media (max-width: 375px) {
            html {
                font-size: 1rem;
            }
        }
        html,body{
            height:100%;
        }
        body {
            margin: 0;
            padding: 0;
            background-color: #f2f2f2;
        }

        p {
            margin: 0;

        }

        .h5-4box {
            height:100%;
            display: -ms-flexbox;
            display: flex;
            -ms-flex-pack: center;
            justify-content: center;
            -ms-flex-align: center;
            align-items: center;
            -ms-flex-direction: column;
            flex-direction: column;
        }

        .h5-4img {
            /* display: inline-block; */

        }

        .h5-4img img {
            max-width: 100%;
        }

        .h5-4con {
            padding-top: 1.875rem;
            font-size: 0.875rem;
            line-height: 1.2;
            color: #636363;
            text-align: center;
        }

        .h5-5con {
            padding-top: 1.875rem;
            font-size: 0.5rem;
            line-height: 1.2;
            color: #636363;
            text-align: center;
        }

        .btn {
            display: inline-block;
            border: #e86b0f solid 1px;
            margin: 0 0 0 5px;
            padding: 0 10px;
            line-height: 25px;
            font-size: .75rem;
            vertical-align: middle;
            color: #FFF;
            border-radius: 3px;
            background-color: #ff8200;
        }
    </style>
</head>
<body>
<div class="h5-4box">
		<div class="h5-4img">
			<img src="//h5.sinaimg.cn/upload/2016/04/11/319/h5-404.png">
		</div>
    <p class="h5-4con">用户不存在</p>
    <br/>
            <div class="h5-5con"><a href="/">返回首页</a></div>    </div>
</body>
</html>
